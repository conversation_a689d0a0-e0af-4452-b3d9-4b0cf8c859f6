"use client"

import { useState, useEffect, useRef } from "react"
import {
  Moon,
  Sun,
  Upload,
  Play,
  Code,
  Zap,
  ImageIcon,
  <PERSON>rkles,
  User,
  Palette,
  ExternalLink,
  Book,
  Activity,
  Download,
  Copy,
  Type,
  Cloud,
  Layers,
  Maximize,
  FileImage,
  Droplets,
  Star,
  Clock,
  TrendingUp,
  Globe,
  Cpu,
  Check,
  ArrowRight,
  Grid3X3,
  Quote,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"

export default function ImgionPlatform() {
  const [isDark, setIsDark] = useState(true)
  const [dragActive, setDragActive] = useState(false)
  const [sliderPosition, setSliderPosition] = useState(50)
  const [activeCodeTab, setActiveCodeTab] = useState("javascript")
  const [statsCounter, setStatsCounter] = useState(2500000)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [credits, setCredits] = useState(150)
  const [demoStep, setDemoStep] = useState(0)
  const [currentDemo, setCurrentDemo] = useState(0)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingStep, setProcessingStep] = useState(0)
  const [stats, setStats] = useState({
    images: 2847392,
    users: 89432,
    processing: 1.2,
  })
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // Demo transformations that auto-cycle
  const demoTransformations = [
    {
      title: "Background Removal",
      before: "Portrait with busy background",
      after: "Clean isolated subject",
      color: "from-purple-500 to-pink-500",
    },
    {
      title: "AI Upscaling",
      before: "Low resolution image",
      after: "Crystal clear 4K quality",
      color: "from-cyan-500 to-blue-500",
    },
    {
      title: "Style Transfer",
      before: "Regular photograph",
      after: "Van Gogh masterpiece",
      color: "from-pink-500 to-orange-500",
    },
  ]

  const features = [
    {
      name: "Background Remover",
      description: "AI-powered background removal in seconds",
      icon: ImageIcon,
      gradient: "from-purple-500 via-pink-500 to-cyan-500",
      time: "0.8s",
      size: "large",
      video: true,
    },
    {
      name: "AI Upscaler",
      description: "Enhance to 4K with zero quality loss",
      icon: Zap,
      gradient: "from-cyan-500 via-blue-500 to-purple-500",
      time: "2.1s",
      size: "large",
      video: true,
    },
    {
      name: "Style Transfer",
      description: "Transform into artistic masterpieces",
      icon: Palette,
      gradient: "from-pink-500 via-orange-500 to-red-500",
      time: "3.2s",
      size: "medium",
      video: true,
    },
    {
      name: "Face Enhancement",
      description: "Professional portrait retouching",
      icon: User,
      gradient: "from-green-500 via-teal-500 to-cyan-500",
      time: "1.4s",
      size: "medium",
      video: false,
    },
    {
      name: "Sky Replacement",
      description: "Change weather and atmosphere",
      icon: Cloud,
      gradient: "from-blue-500 via-indigo-500 to-purple-500",
      time: "1.8s",
      size: "medium",
      video: false,
    },
    {
      name: "Object Removal",
      description: "Erase unwanted elements",
      icon: Sparkles,
      gradient: "from-yellow-500 via-orange-500 to-red-500",
      time: "1.2s",
      size: "small",
      video: false,
    },
    {
      name: "Smart Crop",
      description: "AI-powered composition",
      icon: Grid3X3,
      gradient: "from-teal-500 via-green-500 to-lime-500",
      time: "0.6s",
      size: "small",
      video: false,
    },
    {
      name: "Text Effects",
      description: "Dynamic text overlays",
      icon: Type,
      gradient: "from-rose-500 via-pink-500 to-purple-500",
      time: "0.9s",
      size: "small",
      video: false,
    },
  ]

  const processingSteps = ["Analyzing image...", "Applying AI model...", "Enhancing quality...", "Finalizing..."]

  // Auto-cycle demo transformations
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentDemo((prev) => (prev + 1) % demoTransformations.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  // Animated statistics
  useEffect(() => {
    const interval = setInterval(() => {
      setStats((prev) => ({
        images: prev.images + Math.floor(Math.random() * 5) + 1,
        users: prev.users + Math.floor(Math.random() * 3),
        processing: +(Math.random() * 0.5 + 0.8).toFixed(1),
      }))
    }, 2000)
    return () => clearInterval(interval)
  }, [])

  // Mouse tracking for cursor effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }
    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  // Particle animation canvas
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    canvas.width = window.innerWidth
    canvas.height = window.innerHeight

    const particles: Array<{
      x: number
      y: number
      vx: number
      vy: number
      size: number
      opacity: number
      color: string
    }> = []

    // Create particles
    for (let i = 0; i < 50; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 3 + 1,
        opacity: Math.random() * 0.5 + 0.2,
        color: ["#8B5CF6", "#EC4899", "#06B6D4"][Math.floor(Math.random() * 3)],
      })
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      particles.forEach((particle) => {
        particle.x += particle.vx
        particle.y += particle.vy

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1

        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fillStyle = particle.color + Math.floor(particle.opacity * 255).toString(16)
        ctx.fill()

        // Add glow effect
        ctx.shadowBlur = 20
        ctx.shadowColor = particle.color
        ctx.fill()
        ctx.shadowBlur = 0
      })

      requestAnimationFrame(animate)
    }

    animate()
  }, [])

  const simulateProcessing = () => {
    setIsProcessing(true)
    setProcessingStep(0)

    const stepInterval = setInterval(() => {
      setProcessingStep((prev) => {
        if (prev >= processingSteps.length - 1) {
          clearInterval(stepInterval)
          setTimeout(() => setIsProcessing(false), 1000)
          return prev
        }
        return prev + 1
      })
    }, 800)
  }

  const getGridItemClass = (index: number, size: string) => {
    const baseClass = "group cursor-pointer transition-all duration-500 hover:scale-105"

    if (size === "large") {
      return `${baseClass} col-span-2 row-span-2`
    } else if (size === "medium") {
      return `${baseClass} col-span-2`
    } else {
      return baseClass
    }
  }

  // Animated counter effect
  useEffect(() => {
    const interval = setInterval(() => {
      setStatsCounter((prev) => prev + Math.floor(Math.random() * 3) + 1)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  // Demo animation
  useEffect(() => {
    const interval = setInterval(() => {
      setDemoStep((prev) => (prev + 1) % 4)
    }, 2000)
    return () => clearInterval(interval)
  }, [])

  const codeExamples = {
    javascript: `import { ImgionAI } from '@imgion/sdk';

const imgion = new ImgionAI('your_api_key');

// Remove background
const result = await imgion.removeBackground({
  image: 'https://example.com/photo.jpg',
  format: 'png',
  quality: 'high'
});

console.log(result.url); // Processed image URL
console.log(result.processingTime); // 1.2s`,
    python: `from imgion import ImgionAI

imgion = ImgionAI('your_api_key')

# Remove background
result = imgion.remove_background(
    image='https://example.com/photo.jpg',
    format='png',
    quality='high'
)

print(result.url)  # Processed image URL
print(result.processing_time)  # 1.2s`,
    curl: `curl -X POST https://api.imgion.ai/v1/remove-background \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "image": "https://example.com/photo.jpg",
    "format": "png",
    "quality": "high"
  }'`,
    php: `<?php
require_once 'vendor/autoload.php';

use Imgion\\ImgionAI;

$imgion = new ImgionAI('your_api_key');

$result = $imgion->removeBackground([
    'image' => 'https://example.com/photo.jpg',
    'format' => 'png',
    'quality' => 'high'
]);

echo $result->url; // Processed image URL`,
  }

  const mainFeatures = [
    {
      name: "Background Remover",
      description: "One-click background removal with AI precision",
      icon: ImageIcon,
      gradient: "from-purple-500 to-pink-500",
      processingTime: "0.8s",
      size: "large",
    },
    {
      name: "AI Upscaler",
      description: "Enhance resolution up to 4x without quality loss",
      icon: Zap,
      gradient: "from-pink-500 to-cyan-500",
      processingTime: "2.1s",
      size: "large",
    },
    {
      name: "Style Transfer",
      description: "Apply artistic styles from famous paintings",
      icon: Palette,
      gradient: "from-cyan-500 to-purple-500",
      processingTime: "3.2s",
      size: "large",
    },
    {
      name: "Object Remover",
      description: "Smart object erasure with content-aware fill",
      icon: Sparkles,
      gradient: "from-purple-500 to-cyan-500",
      processingTime: "1.5s",
      size: "large",
    },
    {
      name: "Generative Fill",
      description: "AI-powered content-aware filling",
      icon: Layers,
      gradient: "from-pink-500 to-purple-500",
      processingTime: "2.8s",
      size: "large",
    },
  ]

  const secondaryFeatures = [
    {
      name: "Face Enhancement",
      description: "Portrait retouching and enhancement",
      icon: User,
      gradient: "from-orange-500 to-pink-500",
      processingTime: "1.2s",
      size: "medium",
    },
    {
      name: "Sky Replacement",
      description: "Change skies instantly with AI",
      icon: Cloud,
      gradient: "from-blue-500 to-cyan-500",
      processingTime: "1.8s",
      size: "medium",
    },
    {
      name: "Color Grading",
      description: "Professional color correction",
      icon: Droplets,
      gradient: "from-green-500 to-cyan-500",
      processingTime: "0.9s",
      size: "medium",
    },
    {
      name: "Smart Crop",
      description: "AI-powered composition optimization",
      icon: Grid3X3,
      gradient: "from-indigo-500 to-purple-500",
      processingTime: "0.7s",
      size: "medium",
    },
    {
      name: "Text Overlay",
      description: "Add beautiful text with smart positioning",
      icon: Type,
      gradient: "from-rose-500 to-pink-500",
      processingTime: "0.5s",
      size: "medium",
    },
  ]

  const quickTools = [
    {
      name: "Batch Processing",
      description: "Process multiple images",
      icon: Layers,
      gradient: "from-teal-500 to-green-500",
      processingTime: "varies",
      size: "small",
    },
    {
      name: "Format Converter",
      description: "Convert formats",
      icon: FileImage,
      gradient: "from-yellow-500 to-orange-500",
      processingTime: "0.2s",
      size: "small",
    },
    {
      name: "Compress",
      description: "Optimize file size",
      icon: Maximize,
      gradient: "from-red-500 to-pink-500",
      processingTime: "0.4s",
      size: "small",
    },
    {
      name: "Collage Maker",
      description: "Create photo collages",
      icon: Grid3X3,
      gradient: "from-purple-500 to-indigo-500",
      processingTime: "1.1s",
      size: "small",
    },
    {
      name: "Image to Image",
      description: "Transform with AI",
      icon: Sparkles,
      gradient: "from-pink-500 to-rose-500",
      processingTime: "2.3s",
      size: "small",
    },
  ]

  const pricingPlans = [
    {
      name: "Free",
      price: "$0",
      period: "forever",
      description: "Perfect for trying out our AI tools",
      features: ["10 images per day", "Basic tools only", "720p export", "Watermarked images", "Community support"],
      popular: false,
      cta: "Get Started Free",
    },
    {
      name: "Starter",
      price: "$9",
      period: "per month",
      description: "Great for personal projects and small businesses",
      features: [
        "100 images per day",
        "All AI tools",
        "1080p export",
        "No watermark",
        "Email support",
        "Basic API access (100 calls)",
      ],
      popular: false,
      cta: "Start Free Trial",
    },
    {
      name: "Pro",
      price: "$29",
      period: "per month",
      description: "Perfect for professionals and content creators",
      features: [
        "500 images per day",
        "Priority processing",
        "4K export",
        "Batch processing",
        "API access (1000 calls)",
        "Priority support",
      ],
      popular: true,
      cta: "Start Free Trial",
    },
    {
      name: "Business",
      price: "$99",
      period: "per month",
      description: "For teams and enterprise solutions",
      features: [
        "Unlimited images",
        "White label option",
        "8K export",
        "Team collaboration",
        "Unlimited API calls",
        "Dedicated support",
      ],
      popular: false,
      cta: "Contact Sales",
    },
  ]

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Content Creator",
      company: "@sarahcreates",
      content: "Imgion AI has revolutionized my workflow. What used to take hours in Photoshop now takes seconds!",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      name: "Marcus Rodriguez",
      role: "E-commerce Manager",
      company: "ShopTech Inc.",
      content: "The batch processing feature saved us hundreds of hours preparing product images. ROI was immediate.",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      name: "Emily Watson",
      role: "Marketing Director",
      company: "BrandFlow",
      content: "The API integration was seamless. Our customers love the instant background removal feature.",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const demoSteps = ["Original Image", "AI Processing...", "Background Removed", "Final Result"]

  return (
    <div className={`min-h-screen transition-all duration-500 ${isDark ? "dark bg-gray-950" : "bg-gray-50"}`}>
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-pink-500/10 to-cyan-500/10 rounded-full blur-3xl animate-pulse delay-2000" />
      </div>

      {/* Navigation */}
      <nav className="sticky top-0 z-50 backdrop-blur-xl bg-white/80 dark:bg-gray-950/80 border-b border-gray-200/50 dark:border-gray-800/50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 to-cyan-500 rounded-xl blur opacity-50 animate-pulse" />
              </div>
              <div>
                <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 bg-clip-text text-transparent">
                  Imgion AI
                </span>
                <div className="text-xs text-gray-500 dark:text-gray-400 -mt-1">AI-Powered Image Magic</div>
              </div>
            </div>

            <div className="hidden lg:flex items-center space-x-8">
              <a
                href="#features"
                className="text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors font-medium"
              >
                Features
              </a>
              <a
                href="#pricing"
                className="text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors font-medium"
              >
                Pricing
              </a>
              <a
                href="#api"
                className="text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors font-medium"
              >
                API
              </a>
              <a
                href="#playground"
                className="text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors font-medium"
              >
                Playground
              </a>
              <a
                href="#blog"
                className="text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors font-medium"
              >
                Blog
              </a>
            </div>

            <div className="flex items-center space-x-4">
              {isLoggedIn && (
                <div className="hidden md:flex items-center space-x-2 px-3 py-1 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full border border-purple-500/20">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{credits} credits</span>
                </div>
              )}

              <button
                onClick={() => setIsDark(!isDark)}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                {isDark ? (
                  <Sun className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                ) : (
                  <Moon className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                )}
              </button>

              <Button
                variant="ghost"
                className="text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400"
              >
                Sign In
              </Button>

              <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group">
                <span className="relative z-10">Try Editor</span>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl" />
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        <div className="container mx-auto relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full px-4 py-2 mb-8 border border-purple-500/20">
                <TrendingUp className="w-4 h-4 text-purple-500" />
                <span className="text-sm font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  {statsCounter.toLocaleString()}+ images processed this month
                </span>
              </div>

              <h1 className="text-5xl md:text-7xl font-bold mb-8 bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 bg-clip-text text-transparent leading-tight">
                Transform Images with AI in Seconds
              </h1>

              <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-12 leading-relaxed">
                Professional image editing powered by cutting-edge AI.
                <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent font-semibold">
                  {" "}
                  No design skills required.
                </span>
              </p>

              <div className="flex flex-col sm:flex-row gap-6 mb-8">
                <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-lg px-8 py-4 rounded-xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 relative overflow-hidden group">
                  <span className="relative z-10 flex items-center">
                    <Sparkles className="w-5 h-5 mr-2" />
                    Try Editor Free
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl" />
                </Button>

                <Button
                  variant="outline"
                  className="border-2 border-gray-300 dark:border-gray-600 hover:border-purple-500 dark:hover:border-purple-500 text-lg px-8 py-4 rounded-xl bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm transition-all duration-300 group text-gray-700 dark:text-gray-300"
                >
                  <Play className="w-5 h-5 mr-2 group-hover:text-purple-500 transition-colors" />
                  Watch Demo
                </Button>
              </div>

              <div className="text-sm text-gray-500 dark:text-gray-400">
                ✨ No credit card required • 🚀 Start in 30 seconds • 🎨 Professional results
              </div>
            </div>

            {/* Interactive Demo */}
            <div className="relative">
              <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-2xl overflow-hidden">
                <CardContent className="p-0">
                  <div
                    className={`relative h-80 transition-all duration-300 ${
                      dragActive
                        ? "bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30"
                        : "bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700"
                    }`}
                    onDragEnter={() => setDragActive(true)}
                    onDragLeave={() => setDragActive(false)}
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={(e) => {
                      e.preventDefault()
                      setDragActive(false)
                      simulateProcessing()
                    }}
                  >
                    {/* Upload Area */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div
                        className={`w-full h-full flex flex-col items-center justify-center p-8 border-2 border-dashed transition-all duration-300 cursor-pointer ${
                          dragActive
                            ? "border-purple-500 bg-purple-500/10 scale-105"
                            : "border-gray-300 dark:border-gray-600 hover:border-purple-400 dark:hover:border-purple-500"
                        }`}
                        onClick={() => document.getElementById("file-upload")?.click()}
                      >
                        <input
                          id="file-upload"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            if (e.target.files?.[0]) {
                              simulateProcessing()
                            }
                          }}
                        />

                        {/* Upload Icon */}
                        <div
                          className={`w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center transition-all duration-300 ${
                            dragActive
                              ? "bg-gradient-to-r from-purple-500 to-pink-500 scale-110"
                              : "bg-gradient-to-r from-purple-500 to-pink-500"
                          }`}
                        >
                          <Upload className="w-8 h-8 text-white" />
                        </div>

                        {/* Instructions */}
                        <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
                          {dragActive ? "Drop your image here" : "Drag & drop or click to upload"}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-4 text-center">
                          Transform your images with AI in seconds
                        </p>

                        {/* File Type Indicators */}
                        <div className="flex flex-wrap justify-center gap-2 mb-4">
                          {["JPG", "PNG", "WebP", "HEIC"].map((format) => (
                            <span
                              key={format}
                              className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300"
                            >
                              {format}
                            </span>
                          ))}
                        </div>

                        {/* Max File Size */}
                        <p className="text-xs text-gray-500 dark:text-gray-400">Max file size: 10MB</p>
                      </div>
                    </div>

                    {/* Drag Overlay */}
                    {dragActive && (
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 flex items-center justify-center backdrop-blur-sm">
                        <div className="text-center">
                          <div className="w-24 h-24 mx-auto mb-4 bg-white/90 dark:bg-gray-900/90 rounded-full flex items-center justify-center">
                            <Upload className="w-12 h-12 text-purple-500" />
                          </div>
                          <p className="text-xl font-semibold text-purple-700 dark:text-purple-300">
                            Release to upload
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Processing State */}
                    {isProcessing && (
                      <div className="absolute inset-0 bg-white/95 dark:bg-gray-900/95 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center animate-pulse">
                            <Sparkles className="w-10 h-10 text-white" />
                          </div>
                          <div className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {processingSteps[processingStep]}
                          </div>
                          <div className="w-48 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-500"
                              style={{ width: `${((processingStep + 1) / processingSteps.length) * 100}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* AI Tools Section - Appears after upload */}
                  {!isProcessing && (
                    <div className="p-6 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                      <div className="text-center mb-4">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                          Choose AI Transformation
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300">One-click to apply any effect</p>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        {[
                          { name: "Remove Background", icon: ImageIcon, color: "from-purple-500 to-pink-500" },
                          { name: "Upscale 4K", icon: Zap, color: "from-cyan-500 to-blue-500" },
                          { name: "Style Transfer", icon: Palette, color: "from-pink-500 to-orange-500" },
                          { name: "Face Enhance", icon: User, color: "from-green-500 to-teal-500" },
                        ].map((tool) => (
                          <Button
                            key={tool.name}
                            variant="outline"
                            className="h-12 border-gray-300 dark:border-gray-600 hover:border-purple-500 dark:hover:border-purple-400 group bg-transparent"
                            onClick={() => simulateProcessing()}
                          >
                            <div
                              className={`w-6 h-6 rounded bg-gradient-to-r ${tool.color} flex items-center justify-center mr-2 group-hover:scale-110 transition-transform`}
                            >
                              <tool.icon className="w-4 h-4 text-white" />
                            </div>
                            <span className="text-sm font-medium">{tool.name}</span>
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Upload Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">Start with Your Image</h2>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                Upload, paste, or drag your image to begin the magic
              </p>
            </div>

            <div
              className={`relative p-12 border-2 border-dashed rounded-3xl transition-all duration-300 backdrop-blur-sm ${
                dragActive
                  ? "border-purple-500 bg-purple-500/10 scale-105"
                  : "border-gray-300 dark:border-gray-600 hover:border-purple-400 dark:hover:border-purple-500 bg-white/50 dark:bg-gray-900/50"
              }`}
              onDragEnter={() => setDragActive(true)}
              onDragLeave={() => setDragActive(false)}
              onDragOver={(e) => e.preventDefault()}
              onDrop={(e) => {
                e.preventDefault()
                setDragActive(false)
              }}
            >
              <div className="text-center">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                  <Upload className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">Drop your image here</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Or click to browse, paste from clipboard, or enter a URL
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                  <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white">
                    <Upload className="w-4 h-4 mr-2" />
                    Choose File
                  </Button>
                  <Button
                    variant="outline"
                    className="border-gray-300 dark:border-gray-600 bg-transparent text-gray-700 dark:text-gray-300"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    From URL
                  </Button>
                </div>

                <div className="flex flex-wrap justify-center gap-2">
                  {["JPG", "PNG", "WebP", "HEIC"].map((format) => (
                    <span
                      key={format}
                      className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      {format}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Showcase - Bento Grid */}
      <section id="features" className="py-20 px-4 bg-gray-50/50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 bg-clip-text text-transparent">
              AI-Powered Image Editing
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Professional image editing tools powered by cutting-edge artificial intelligence
            </p>
          </div>

          {/* Bento Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
            {/* Main Features - Large Cards */}
            {mainFeatures.map((feature, index) => (
              <Card
                key={feature.name}
                className={`group hover:scale-105 transition-all duration-500 cursor-pointer border-0 shadow-xl hover:shadow-2xl backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 ${
                  index < 2 ? "md:col-span-2" : index === 2 ? "lg:col-span-2" : ""
                }`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardContent className="p-8">
                  <div className="flex items-start justify-between mb-6">
                    <div
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <Clock className="w-4 h-4" />
                      <span>{feature.processingTime}</span>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold mb-3 text-gray-900 dark:text-white group-hover:bg-gradient-to-r group-hover:from-purple-600 group-hover:to-pink-600 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                    {feature.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">{feature.description}</p>
                  <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 opacity-0 group-hover:opacity-100 transition-all duration-300 text-white">
                    Try Now
                  </Button>
                </CardContent>
              </Card>
            ))}

            {/* Secondary Features - Medium Cards */}
            {secondaryFeatures.map((feature, index) => (
              <Card
                key={feature.name}
                className={`group hover:scale-105 transition-all duration-500 cursor-pointer border-0 shadow-lg hover:shadow-xl backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 ${
                  index < 3 ? "lg:col-span-2" : ""
                }`}
                style={{ animationDelay: `${(index + 5) * 100}ms` }}
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div
                      className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                      <Clock className="w-3 h-3" />
                      <span>{feature.processingTime}</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                    {feature.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">{feature.description}</p>
                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full opacity-0 group-hover:opacity-100 transition-all duration-300 bg-transparent text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"
                  >
                    Try Now
                  </Button>
                </CardContent>
              </Card>
            ))}

            {/* Quick Tools - Small Cards */}
            {quickTools.map((feature, index) => (
              <Card
                key={feature.name}
                className="group hover:scale-105 transition-all duration-500 cursor-pointer border-0 shadow-md hover:shadow-lg backdrop-blur-sm bg-white/80 dark:bg-gray-900/80"
                style={{ animationDelay: `${(index + 10) * 100}ms` }}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div
                      className={`w-10 h-10 rounded-lg bg-gradient-to-r ${feature.gradient} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <feature.icon className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                      <Clock className="w-3 h-3" />
                      <span>{feature.processingTime}</span>
                    </div>
                  </div>
                  <h3 className="font-semibold mb-1 text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                    {feature.name}
                  </h3>
                  <p className="text-xs text-gray-600 dark:text-gray-300">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 bg-clip-text text-transparent">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Choose the perfect plan for your needs. Start free, upgrade anytime.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <Card
                key={plan.name}
                className={`relative transition-all duration-300 hover:scale-105 backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 border-0 shadow-xl hover:shadow-2xl ${
                  plan.popular ? "ring-2 ring-purple-500 scale-105" : ""
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">{plan.name}</h3>
                    <div className="mb-4">
                      <span className="text-4xl font-bold text-gray-900 dark:text-white">{plan.price}</span>
                      <span className="text-gray-600 dark:text-gray-300 ml-2">{plan.period}</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{plan.description}</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-center">
                        <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    className={`w-full ${
                      plan.popular
                        ? "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                        : "bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white"
                    }`}
                  >
                    {plan.cta}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              All plans include a 14-day free trial. No credit card required.
            </p>
            <Button
              variant="outline"
              className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-transparent"
            >
              Compare All Features
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 px-4 bg-gray-50/50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-gray-900 dark:text-white">Loved by Creators Worldwide</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Join thousands of professionals who trust Imgion AI
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className="backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <CardContent className="p-8">
                  <Quote className="w-8 h-8 text-purple-500 mb-4" />
                  <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">"{testimonial.content}"</p>
                  <div className="flex items-center">
                    <img
                      src={testimonial.avatar || "/placeholder.svg"}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">{testimonial.name}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {testimonial.role} • {testimonial.company}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* API Section */}
      <section id="api" className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 bg-clip-text text-transparent">
              Developer API
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Integrate AI image processing into your applications with our simple, powerful API
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
            {/* Code Editor */}
            <div className="space-y-6">
              <div className="flex space-x-1">
                {Object.keys(codeExamples).map((lang) => (
                  <button
                    key={lang}
                    onClick={() => setActiveCodeTab(lang)}
                    className={`px-4 py-2 rounded-t-lg font-medium transition-all duration-300 ${
                      activeCodeTab === lang
                        ? "bg-gray-900 dark:bg-gray-800 text-white border-b-2 border-purple-500"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                    }`}
                  >
                    {lang === "javascript" ? "JavaScript" : lang === "python" ? "Python" : lang.toUpperCase()}
                  </button>
                ))}
              </div>

              <Card className="bg-gray-900 dark:bg-gray-800 border-gray-700 overflow-hidden">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full" />
                        <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                        <div className="w-3 h-3 bg-green-500 rounded-full" />
                      </div>
                      <span className="text-sm text-gray-400 ml-4">
                        {activeCodeTab === "javascript"
                          ? "main.js"
                          : activeCodeTab === "python"
                            ? "main.py"
                            : "terminal"}
                      </span>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(codeExamples[activeCodeTab as keyof typeof codeExamples])}
                      className="text-gray-400 hover:text-white"
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <pre className="text-sm text-gray-300 p-6 overflow-x-auto font-mono leading-relaxed">
                    <code>{codeExamples[activeCodeTab as keyof typeof codeExamples]}</code>
                  </pre>
                </CardContent>
              </Card>
            </div>

            {/* API Stats and Features */}
            <div className="space-y-6">
              <Card className="backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 border-0 shadow-xl">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">API Performance</h3>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent mb-2">
                        99.9%
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">Uptime</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent mb-2">
                        &lt;2s
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">Avg Response</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent mb-2">
                        50M+
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">Requests/Month</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent mb-2">
                        15+
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">AI Models</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="backdrop-blur-sm bg-white/80 dark:bg-gray-900/80 border-0 shadow-xl">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Developer Resources</h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                      <Book className="w-5 h-5 text-blue-500" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">Documentation</div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Complete API reference</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                      <Download className="w-5 h-5 text-green-500" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">SDKs</div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Official libraries</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                      <Activity className="w-5 h-5 text-purple-500" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">Status Page</div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">Real-time monitoring</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-lg py-4 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 relative overflow-hidden group">
                <span className="relative z-10 flex items-center justify-center">
                  <Code className="w-5 h-5 mr-2" />
                  Get API Key - Free
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-16 px-4 border-t border-gray-800">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 to-cyan-500 rounded-xl flex items-center justify-center">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 to-cyan-500 rounded-xl blur opacity-50" />
                </div>
                <div>
                  <span className="text-2xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                    Imgion AI
                  </span>
                  <div className="text-xs text-gray-400 -mt-1">AI-Powered Image Magic</div>
                </div>
              </div>
              <p className="text-gray-400 leading-relaxed">
                Transform your images with the power of AI. Professional results in seconds, whether you're a designer,
                developer, or creator.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-white">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    API
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Playground
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Integrations
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-white">Developers</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Documentation
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    SDKs
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    API Reference
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Status
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Changelog
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-white">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    About
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Careers
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Contact
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    Privacy
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400">&copy; 2024 Imgion AI. All rights reserved.</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <Globe className="w-4 h-4" />
                <span>Global CDN</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <Cpu className="w-4 h-4" />
                <span>Edge Computing</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
